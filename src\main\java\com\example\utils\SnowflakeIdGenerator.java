package com.example.utils;

import org.springframework.stereotype.Component;
/**
 * 雪花算法ID生成器
 * 64位ID结构：
 * 1位符号位 + 41位时间戳 + 5位数据中心ID + 5位工作机器ID + 12位序列号
 * 特点：
 * 1. 生成的ID整体上按时间自增
 * 2. 不依赖数据库等第三方系统
 * 3. 理论上QPS可达到409.6万/秒(2^12)
 * 
 * <AUTHOR>
 */
@Component
public class SnowflakeIdGenerator {
    /**
     * 开始时间戳 (2025-06-11 00:00:00.000)
     * 使用这个时间戳作为基准，可以支持到2090年
     */
    private static final long START_TIMESTAMP = 1749744000000L;

    /**
     * 数据中心ID所占位数
     */
    private static final long DATA_CENTER_ID_BITS = 5L;
    
    /**
     * 工作机器ID所占位数
     */
    private static final long WORKER_ID_BITS = 5L;
    
    /**
     * 序列号所占位数
     */
    private static final long SEQUENCE_BITS = 12L;

    /**
     * 数据中心ID最大值
     */
    private static final long MAX_DATA_CENTER_ID = ~(-1L << DATA_CENTER_ID_BITS);
    
    /**
     * 工作机器ID最大值
     */
    private static final long MAX_WORKER_ID = ~(-1L << WORKER_ID_BITS);
    
    /**
     * 序列号最大值
     */
    private static final long MAX_SEQUENCE = ~(-1L << SEQUENCE_BITS);

    /**
     * 工作机器ID左移位数
     */
    private static final long WORKER_ID_SHIFT = SEQUENCE_BITS;
    
    /**
     * 数据中心ID左移位数
     */
    private static final long DATA_CENTER_ID_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS;
    
    /**
     * 时间戳左移位数
     */
    private static final long TIMESTAMP_SHIFT = SEQUENCE_BITS + WORKER_ID_BITS + DATA_CENTER_ID_BITS;

    /**
     * 数据中心ID
     */
    private final long dataCenterId;
    
    /**
     * 工作机器ID
     */
    private final long workerId;
    
    /**
     * 上次生成ID的时间戳
     */
    private long lastTimestamp = -1L;
    
    /**
     * 序列号
     */
    private long sequence = 0L;

    /**
     * 默认构造函数，使用默认的数据中心ID和工作机器ID
     */
    public SnowflakeIdGenerator() {
        this(1, 1);
    }

    /**
     * 构造函数
     * @param dataCenterId 数据中心ID (0~31)
     * @param workerId 工作机器ID (0~31)
     * @throws IllegalArgumentException 如果数据中心ID或工作机器ID超出范围
     */
    private SnowflakeIdGenerator(long dataCenterId, long workerId) {
        if (dataCenterId > MAX_DATA_CENTER_ID || dataCenterId < 0) {
            throw new IllegalArgumentException("Data center ID can't be greater than " + MAX_DATA_CENTER_ID + " or less than 0");
        }
        if (workerId > MAX_WORKER_ID || workerId < 0) {
            throw new IllegalArgumentException("Worker ID can't be greater than " + MAX_WORKER_ID + " or less than 0");
        }
        this.dataCenterId = dataCenterId;
        this.workerId = workerId;
    }

    /**
     * 生成下一个ID
     * 使用synchronized保证线程安全
     * 
     * @return 64位长整型ID
     * @throws IllegalStateException 如果系统时钟回拨
     */
    public synchronized long nextId() {
        long timestamp = getCurrentTimestamp();
        
        // 处理时钟回拨
        if (timestamp < lastTimestamp) {
            throw new IllegalStateException("Clock moved backwards. Refusing to generate ID.");
        }
        
        // 同一毫秒内序列号自增
        if (timestamp == lastTimestamp) {
            sequence = (sequence + 1) & MAX_SEQUENCE;
            // 序列号用完后等待下一毫秒
            if (sequence == 0) {
                timestamp = getNextTimestamp(lastTimestamp);
            }
        } else {
            // 不同毫秒内序列号重置为0
            sequence = 0L;
        }
        
        lastTimestamp = timestamp;
        
        // 组合ID
        return ((timestamp - START_TIMESTAMP) << TIMESTAMP_SHIFT) |
                (dataCenterId << DATA_CENTER_ID_SHIFT) |
                (workerId << WORKER_ID_SHIFT) |
                sequence;
    }

    /**
     * 获取当前时间戳
     * @return 当前时间戳（毫秒）
     */
    private long getCurrentTimestamp() {
        return System.currentTimeMillis();
    }

    /**
     * 获取下一个时间戳
     * 当序列号用完后，等待下一毫秒
     * 
     * @param lastTimestamp 上次生成ID的时间戳
     * @return 下一个可用的时间戳
     */
    private long getNextTimestamp(long lastTimestamp) {
        long timestamp = getCurrentTimestamp();
        while (timestamp <= lastTimestamp) {
            timestamp = getCurrentTimestamp();
        }
        return timestamp;
    }
}

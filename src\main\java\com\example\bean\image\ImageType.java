package com.example.bean.image;

import com.example.bean.hitokoto.HitokotoType;
import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * <AUTHOR>
 */
public enum ImageType {
    EMOTICON,
    AVATAR,
    WALLPAPER,
    PHOTOGRAPH,
    GIF,
    OTHER;

    /**
     * 通过字符串值获取对应的枚举类型，忽略大小写。
     * 若无匹配项，返回 OTHER。
     *
     * @param value 字符串类型
     * @return 对应的 HitokotoType 枚举
     */
    @JsonCreator
    public static ImageType from(String value) {
        if (value == null) {
            return OTHER;
        }
        try {
            return ImageType.valueOf(value.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            return OTHER;
        }
    }

    /**
     * 获取枚举的字符串值（小写），用于序列化。
     *
     * @return 小写字符串
     */
    @JsonValue
    public String toValue() {
        return this.name().toLowerCase();
    }
}

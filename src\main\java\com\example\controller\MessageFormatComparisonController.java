package com.example.controller;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.memory.repository.jdbc.JdbcChatMemoryRepository;
import org.springframework.ai.chat.messages.AssistantMessage;
import org.springframework.ai.chat.messages.Message;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * Message对象 vs 纯文本格式对比示例
 * 展示两种方式在实际使用中的具体区别
 * 
 * <AUTHOR>
 */
@RestController
public class MessageFormatComparisonController {

    @Resource
    private DeepSeekChatModel chatModel;
    
    @Resource
    private JdbcChatMemoryRepository chatMemoryRepository;
    
    private ChatClient messageChatClient;  // Message对象方式
    private ChatClient promptChatClient;   // 纯文本方式
    private ChatMemory inspectMemory;      // 用于查看存储的消息

    @PostConstruct
    public void init() {
        // Message对象方式的ChatMemory
        ChatMemory messageChatMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(10)
                .build();
                
        // 纯文本方式的ChatMemory
        ChatMemory promptChatMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(10)
                .build();
        
        // 用于查看的ChatMemory
        this.inspectMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(10)
                .build();
        
        // 创建两种ChatClient
        this.messageChatClient = ChatClient.builder(chatModel)
                .defaultAdvisors(MessageChatMemoryAdvisor.builder(messageChatMemory).build())
                .build();
                
        this.promptChatClient = ChatClient.builder(chatModel)
                .defaultAdvisors(PromptChatMemoryAdvisor.builder(promptChatMemory).build())
                .build();
    }

    /**
     * Message对象方式 - 保持完整的消息结构
     */
    @GetMapping("/format/message-object")
    public Map<String, Object> messageObjectFormat(
            @RequestParam(value = "message", defaultValue = "我叫张三，今年25岁") String message,
            @RequestParam(value = "conversationId", defaultValue = "message-format-test") String conversationId) {
        
        try {
            String aiReply = messageChatClient.prompt()
                    .user(message)
                    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
                    .call()
                    .content();
            
            // 获取当前对话中的所有消息来展示结构
            List<Message> messages = inspectMemory.get(conversationId);
            List<Map<String, Object>> messageStructure = messages.stream()
                    .map(msg -> Map.of(
                        "type", msg.getClass().getSimpleName(),
                        "role", getRoleFromMessage(msg),
                        "content", msg.getContent(),
                        "hasMetadata", msg.getMetadata() != null && !msg.getMetadata().isEmpty()
                    ))
                    .collect(Collectors.toList());
            
            return Map.of(
                "method", "Message对象方式 (MessageChatMemoryAdvisor)",
                "description", "每条消息保持为独立的Message对象，包含类型、角色、内容、元数据等完整信息",
                "conversationId", conversationId,
                "userMessage", message,
                "aiReply", aiReply,
                "messageStructure", messageStructure,
                "advantages", List.of(
                    "保持消息的完整结构",
                    "支持不同类型的消息（UserMessage、AssistantMessage、SystemMessage）",
                    "保留元数据信息",
                    "AI模型能够区分不同角色的消息"
                )
            );
            
        } catch (Exception e) {
            return Map.of("error", e.getMessage());
        }
    }

    /**
     * 纯文本方式 - 将历史消息转换为文本格式
     */
    @GetMapping("/format/plain-text")
    public Map<String, Object> plainTextFormat(
            @RequestParam(value = "message", defaultValue = "我叫李四，今年30岁") String message,
            @RequestParam(value = "conversationId", defaultValue = "text-format-test") String conversationId) {
        
        try {
            String aiReply = promptChatClient.prompt()
                    .user(message)
                    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
                    .call()
                    .content();
            
            // 模拟纯文本方式的格式（实际格式由PromptChatMemoryAdvisor内部处理）
            List<Message> messages = inspectMemory.get(conversationId);
            String textFormat = messages.stream()
                    .map(msg -> getRoleFromMessage(msg) + ": " + msg.getContent())
                    .collect(Collectors.joining("\n"));
            
            return Map.of(
                "method", "纯文本方式 (PromptChatMemoryAdvisor)",
                "description", "将历史消息转换为纯文本格式，追加到系统提示中",
                "conversationId", conversationId,
                "userMessage", message,
                "aiReply", aiReply,
                "textFormat", textFormat,
                "advantages", List.of(
                    "格式简单直观",
                    "节省token使用",
                    "易于理解和调试",
                    "适合简单的文本对话"
                )
            );
            
        } catch (Exception e) {
            return Map.of("error", e.getMessage());
        }
    }

    /**
     * 对比两种格式的实际效果
     */
    @GetMapping("/format/comparison")
    public Map<String, Object> formatComparison() {
        return Map.of(
            "title", "Message对象 vs 纯文本格式对比",
            "scenarios", Map.of(
                "简单对话", Map.of(
                    "description", "基本的问答对话",
                    "messageObject", "每条消息独立存储，保持完整结构",
                    "plainText", "转换为 'User: 问题\\nAssistant: 回答' 格式",
                    "recommendation", "两种方式效果相似，纯文本更简洁"
                ),
                "复杂对话", Map.of(
                    "description", "包含系统消息、多轮对话、元数据",
                    "messageObject", "保持所有消息类型和元数据，AI能准确理解上下文",
                    "plainText", "可能丢失消息类型信息，但仍能保持基本对话流程",
                    "recommendation", "Message对象方式更适合"
                ),
                "长对话", Map.of(
                    "description", "历史消息很多的长对话",
                    "messageObject", "每条消息占用更多token",
                    "plainText", "压缩为文本，节省token",
                    "recommendation", "纯文本方式更经济"
                )
            ),
            "testSteps", Map.of(
                "step1", "测试Message对象: /format/message-object?message=我叫张三&conversationId=test1",
                "step2", "继续对话: /format/message-object?message=我多大了？&conversationId=test1",
                "step3", "测试纯文本: /format/plain-text?message=我叫李四&conversationId=test2",
                "step4", "继续对话: /format/plain-text?message=我多大了？&conversationId=test2"
            )
        );
    }

    /**
     * 辅助方法：从Message对象获取角色
     */
    private String getRoleFromMessage(Message message) {
        if (message instanceof UserMessage) {
            return "User";
        } else if (message instanceof AssistantMessage) {
            return "Assistant";
        } else {
            return "System";
        }
    }
}

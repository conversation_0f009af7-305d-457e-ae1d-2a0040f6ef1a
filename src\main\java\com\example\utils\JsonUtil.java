package com.example.utils;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.nio.file.Files;

/**
 * JSON 工具类，封装了常用的 JSON 文件操作方法。
 * <p>
 * 主要功能：
 * <ul>
 *     <li>从 JSON 文件读取对象列表</li>
 *     <li>从对象列表中随机获取一个元素</li>
 *     <li>将对象列表保存为 JSON 文件</li>
 * </ul>
 * <p>
 * 使用 Jackson 作为 JSON 解析库。
 *
 * <AUTHOR>
 */
public class JsonUtil {
    private static final ObjectMapper MAPPER = new ObjectMapper();
    private static final Random RANDOM = new Random();

    /**
     * 从指定的 JSON 文件中读取一个对象列表，并随机返回其中的一个元素。
     *
     * @param file    JSON 文件
     * @param typeRef 列表元素类型引用
     * @param <T>     列表元素类型
     * @return 随机元素，若列表为空或文件不存在/为空则返回 null
     * @throws IOException 文件读取或解析异常
     */
    public static <T> T getRandomElement(File file, TypeReference<List<T>> typeRef) throws IOException {
        List<T> list = getList(file, typeRef);
        if (list == null || list.isEmpty()) {
            return null;
        }
        return list.get(RANDOM.nextInt(list.size()));
    }

    /**
     * 从指定的 JSON 文件中读取对象列表。
     *
     * @param file    JSON 文件
     * @param typeRef 列表元素类型引用
     * @param <T>     列表元素类型
     * @return 对象列表，若文件不存在或为空则返回空列表
     * @throws IOException 文件读取或解析异常
     */
    public static <T> List<T> getList(File file, TypeReference<List<T>> typeRef) throws IOException {
        if (file == null || !file.exists() || file.length() == 0) {
            return new ArrayList<>();
        }
        return MAPPER.readValue(file, typeRef);
    }

    /**
     * 将对象列表保存为 JSON 文件，自动创建父目录。
     *
     * @param file 目标 JSON 文件
     * @param list 要保存的对象列表
     * @param <T>  列表元素类型
     * @throws IOException 文件写入异常
     */
    public static <T> void saveToJson(File file, List<T> list) throws IOException {
        if (file == null) {
            throw new IllegalArgumentException("File must not be null");
        }
        // 自动创建父目录
        if (file.getParentFile() != null && !file.getParentFile().exists()) {
            Files.createDirectories(file.getParentFile().toPath());
        }
        MAPPER.writerWithDefaultPrettyPrinter().writeValue(file, list);
    }
}

package com.example.service.serviceImpl;

import com.example.bean.image.Image;
import com.example.bean.image.ImageType;
import com.example.bean.image.ImageWrapper;
import com.example.service.ImageService;
import com.example.utils.DateFormatter;
import com.example.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Service
public class ImageServiceImpl implements ImageService {
    @Value("${image.jsonPath}")
    private String jsonPath;

    @Value("${image.imagePath}")
    private String imageBasePath;

    @Override
    public ImageWrapper getImage() throws IOException {
        Image image = JsonUtil.getRandomElement(new File(jsonPath), new TypeReference<List<Image>>() {
        });
        String imagePath = imageBasePath + File.separator + image.getType().toValue() +
                File.separator + image.getUuid() + "." + image.getFileFormat();
        byte[] imageData = Files.readAllBytes(Paths.get(imagePath));
        String mediaType = getMediaTypeFromFileFormat(image.getFileFormat());

        return new ImageWrapper(imageData, mediaType);
    }

    @Override
    public void saveImage(MultipartFile file, ImageType type) throws IOException {
        Image image = new Image();

        image.setUuid(UUID.randomUUID().toString());
        image.setFileFormat(file.getContentType());
        image.setType(type);
        image.setCreateTime(DateFormatter.getLocalDateTime());

        image.setSize(formatFileSize(file.getSize()));

        File jsonFile = new File(jsonPath);
        List<Image> list = JsonUtil.getList(jsonFile, new TypeReference<List<Image>>() {});
        list.add(image);
        JsonUtil.saveToJson(jsonFile, list);
        imageBasePath = Paths.get(System.getProperty("user.dir"), imageBasePath).toString();
        file.transferTo(new File(imageBasePath + File.separator + image.getType().toValue()
                + File.separator + image.getUuid() + "." + file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1)));
    }


    // 辅助方法：根据文件扩展名返回对应的MIME类型
    private String getMediaTypeFromFileFormat(String fileFormat) {
        return switch (fileFormat.toLowerCase()) {
            case "jpg", "jpeg" -> "image/jpeg";
            case "png" -> "image/png";
            case "gif" -> "image/gif";
            case "bmp" -> "image/bmp";
            case "webp" -> "image/webp";
            default -> "application/octet-stream";
        };
    }

    private String formatFileSize(long sizeInBytes) {
        if (sizeInBytes < 1024) {
            return sizeInBytes + " B";
        } else if (sizeInBytes < 1024 * 1024) {
            return String.format("%.2f KB", sizeInBytes / 1024.0);
        } else {
            return String.format("%.2f MB", sizeInBytes / (1024.0 * 1024));
        }
    }

}

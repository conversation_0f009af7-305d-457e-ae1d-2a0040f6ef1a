package com.example.exception;

import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.validation.FieldError;
import org.springframework.web.bind.MethodArgumentNotValidException;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.context.request.ServletWebRequest;
import org.springframework.web.context.request.WebRequest;

import java.time.OffsetDateTime;
import java.util.HashMap;
import java.util.Map;
/**
 * 全局异常处理器，用于捕获并统一处理Spring MVC中的异常。
 * 当前实现主要处理参数校验异常(MethodArgumentNotValidException)。
 *
 * <AUTHOR>
 */
@ControllerAdvice
public class GlobalExceptionHandler {

    /**
     * 处理参数校验失败异常(MethodArgumentNotValidException)。
     *
     * @param ex 捕获到的参数校验异常
     * @param request 当前请求对象，用于获取请求路径
     * @return 统一格式的错误响应体
     */
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public ResponseEntity<?> handleValidationExceptions(MethodArgumentNotValidException ex, WebRequest request) {
        // 收集所有字段的校验错误信息
        Map<String, String> fieldErrors = new HashMap<>();
        for (FieldError error : ex.getBindingResult().getFieldErrors()) {
            fieldErrors.put(error.getField(), error.getDefaultMessage());
        }

        // 构建响应体
        Map<String, Object> responseBody = new HashMap<>();
        responseBody.put("timestamp", OffsetDateTime.now().toString());
        responseBody.put("status", HttpStatus.BAD_REQUEST.value());
        responseBody.put("error", HttpStatus.BAD_REQUEST.getReasonPhrase());
        responseBody.put("message", fieldErrors);
        // 获取请求路径，若无法获取则为unknown
        String path = (request instanceof ServletWebRequest) ? ((ServletWebRequest) request).getRequest().getRequestURI() : "unknown";
        responseBody.put("path", path);

        return new ResponseEntity<>(responseBody, HttpStatus.BAD_REQUEST);
    }
}

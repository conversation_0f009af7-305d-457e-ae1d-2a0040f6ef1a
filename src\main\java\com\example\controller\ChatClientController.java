package com.example.controller;

import jakarta.annotation.Resource;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.memory.repository.ChatMemoryRepository;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

/**
 * 使用ChatClient和Memory Advisor的聊天控制器
 * 演示了Spring AI中记忆顾问的使用方式
 * 
 * <AUTHOR>
 */
@RestController
public class ChatClientController {

    @Resource
    private DeepSeekChatModel chatModel;
    
    @Resource
    private ChatMemoryRepository chatMemoryRepository;

    /**
     * 使用MessageChatMemoryAdvisor的聊天接口
     * 将历史消息作为Message对象添加到Prompt中
     */
    @GetMapping("/ai/chat-with-message-advisor")
    public String chatWithMessageAdvisor(
            @RequestParam(value = "message", defaultValue = "Hello") String message,
            @RequestParam(value = "conversationId", defaultValue = "message-advisor") String conversationId) {
        
        // 创建ChatMemory
        ChatMemory chatMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(10)
                .build();
        
        // 创建带有MessageChatMemoryAdvisor的ChatClient
        ChatClient chatClient = ChatClient.builder(chatModel)
                .defaultAdvisors(MessageChatMemoryAdvisor.builder(chatMemory).build())
                .build();
        
        // 发送消息并自动管理记忆
        return chatClient.prompt()
                .user(message)
                .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
                .call()
                .content();
    }

    /**
     * 使用PromptChatMemoryAdvisor的聊天接口
     * 将历史消息作为纯文本追加到系统提示中
     */
    @GetMapping("/ai/chat-with-prompt-advisor")
    public String chatWithPromptAdvisor(
            @RequestParam(value = "message", defaultValue = "Hello") String message,
            @RequestParam(value = "conversationId", defaultValue = "prompt-advisor") String conversationId) {
        
        // 创建ChatMemory
        ChatMemory chatMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(10)
                .build();
        
        // 创建带有PromptChatMemoryAdvisor的ChatClient
        ChatClient chatClient = ChatClient.builder(chatModel)
                .defaultAdvisors(PromptChatMemoryAdvisor.builder(chatMemory).build())
                .build();
        
        // 发送消息并自动管理记忆
        return chatClient.prompt()
                .user(message)
                .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
                .call()
                .content();
    }

    /**
     * 比较不同记忆顾问的效果
     */
    @GetMapping("/ai/compare-advisors")
    public String compareAdvisors() {
        return """
                记忆顾问比较：
                
                1. MessageChatMemoryAdvisor:
                   - 访问: /ai/chat-with-message-advisor?message=我叫张三&conversationId=test1
                   - 然后: /ai/chat-with-message-advisor?message=我叫什么名字？&conversationId=test1
                
                2. PromptChatMemoryAdvisor:
                   - 访问: /ai/chat-with-prompt-advisor?message=我叫李四&conversationId=test2
                   - 然后: /ai/chat-with-prompt-advisor?message=我叫什么名字？&conversationId=test2
                
                两种方式都能记住对话历史，但处理方式不同：
                - MessageChatMemoryAdvisor: 保持消息的完整结构
                - PromptChatMemoryAdvisor: 将历史转换为文本格式
                """;
    }
}

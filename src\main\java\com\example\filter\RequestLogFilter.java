package com.example.filter;

import com.example.utils.SnowflakeIdGenerator;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.annotation.Resource;
import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MDC;
import org.springframework.stereotype.Component;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.util.ContentCachingResponseWrapper;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 请求日志过滤器
 * 用于记录所有用户请求信息，包括请求参数、响应结果、处理时间等
 * 支持配置忽略的URL路径
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class RequestLogFilter extends OncePerRequestFilter {

    private static final String REQUEST_ID_KEY = "reqId";
    private static final int HTTP_OK = 200;
    private static final String ERROR_MESSAGE = "参数序列化失败";

    @Resource
    private SnowflakeIdGenerator generator;

    /**
     * 需要忽略的URL路径集合
     */
    private final Set<String> ignores = Set.of();

    /**
     * 流式响应路径集合 - 这些路径不使用响应缓存以支持流式传输
     */
    private final Set<String> streamingPaths = Set.of("/ai/generateStream");

    /**
     * JSON序列化工具
     */
    private final ObjectMapper objectMapper = new ObjectMapper();

    /**
     * 过滤器核心处理方法
     *
     * @param request     HTTP请求
     * @param response    HTTP响应
     * @param filterChain 过滤器链
     * @throws ServletException Servlet异常
     * @throws IOException      IO异常
     */
    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {
        String servletPath = request.getServletPath();

        if (isIgnoreUrl(servletPath)) {
            filterChain.doFilter(request, response);
            return;
        }

        long startTime = System.currentTimeMillis();
        logRequestStart(request);

        // 对于流式响应路径，不使用响应缓存以支持实时流式传输
        if (isStreamingPath(servletPath)) {
            try {
                filterChain.doFilter(request, response);
                logStreamingRequestEnd(startTime);
            } finally {
                // 流式响应不需要特殊处理
            }
        } else {
            // 非流式响应使用原有的缓存机制
            ContentCachingResponseWrapper wrapper = new ContentCachingResponseWrapper(response);
            try {
                filterChain.doFilter(request, wrapper);
                logRequestEnd(wrapper, startTime);
            } finally {
                wrapper.copyBodyToResponse();
            }
        }
    }

    /**
     * 检查URL是否需要被忽略
     *
     * @param url 请求URL
     * @return 如果URL需要被忽略返回true，否则返回false
     */
    private boolean isIgnoreUrl(String url) {
        return ignores.stream().anyMatch(url::startsWith);
    }

    /**
     * 检查URL是否为流式响应路径
     *
     * @param url 请求URL
     * @return 如果URL是流式响应路径返回true，否则返回false
     */
    private boolean isStreamingPath(String url) {
        return streamingPaths.stream().anyMatch(url::startsWith);
    }

    /**
     * 记录请求结束信息
     *
     * @param wrapper   响应包装器
     * @param startTime 请求开始时间
     */
    private void logRequestEnd(ContentCachingResponseWrapper wrapper, long startTime) {
        long time = System.currentTimeMillis() - startTime;
        int status = wrapper.getStatus();
        String contentType = wrapper.getContentType();
        byte[] contentBytes = wrapper.getContentAsByteArray();

        String content;

        if (contentType != null && contentType.startsWith("image/")) {
            long bytes = contentBytes.length;
            String size;

            if (bytes >= 1024 * 1024) {
                size = String.format("%.2f MB", bytes / 1024.0 / 1024.0);
            } else if (bytes >= 1024) {
                size = String.format("%.2f KB", bytes / 1024.0);
            } else {
                size = bytes + " bytes";
            }
            content = "[图片响应] 类型: " + contentType + " | 大小: " + size;
        } else {
            content = new String(contentBytes);
        }

        log.info("请求处理耗时: {}ms | 状态: {} | 响应结果: {}", time, status, content);
    }

    /**
     * 记录流式请求结束信息
     *
     * @param startTime 请求开始时间
     */
    private void logStreamingRequestEnd(long startTime) {
        long time = System.currentTimeMillis() - startTime;
        log.info("流式请求处理耗时: {}ms | 响应类型: 流式响应 (Server-Sent Events)", time);
    }

    /**
     * 记录请求开始信息
     *
     * @param request HTTP请求
     */
    private void logRequestStart(HttpServletRequest request) {
        long reqId = generator.nextId();
        MDC.put(REQUEST_ID_KEY, String.valueOf(reqId));

        Map<String, String> paramMap = new HashMap<>();
        request.getParameterMap().forEach((k, v) -> paramMap.put(k, v.length > 0 ? v[0] : null));

        String paramsJson;
        try {
            paramsJson = objectMapper.writeValueAsString(paramMap);
        } catch (Exception e) {
            log.warn("请求参数序列化失败", e);
            paramsJson = ERROR_MESSAGE;
        }

        log.info("请求URL: \"{}\" ({}) | 远程IP地址: {} | 请求参数列表: {}",
                request.getServletPath(), request.getMethod(), request.getRemoteAddr(), paramsJson);
    }
}

package com.example.bean.hitokoto;

import jakarta.validation.constraints.NotBlank;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 一言实体类，用于封装从 Hitokoto API 返回的一句话内容。
 * <p>
 * 包括内容、出处、作者等基础信息。
 * <p>
 * 字段说明：
 * - uuid：每条句子的唯一标识符
 * - hitokoto：句子内容
 * - type：句子分类（如 a=动画，b=漫画等）
 * - from：出处（作品名）
 * - fromWho：作者、角色等
 * - createTime：创建时间（Unix 时间戳）
 * - length：句子长度
 * <p>
 * 使用 Lombok 注解简化构造方法和 Getter/Setter
 *
 * <AUTHOR>
 * @Date 2025/5/31 20:02
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class Hitokoto {
    /**
     * 每条句子的唯一标识符（UUID）
     */
    private String uuid;

    /**
     * 句子内容，不能为空
     */
    @NotBlank(message = "内容不能为空")
    private String hitokoto;

    /**
     * 句子分类（如 ANIME=动画, GAME=游戏, MOVIE=电影, LITERATURE=文学, QUOTE=名言, LOVE=情感, INTERNET=网络, MUSIC=音乐, OTHER=其他）
     */
    private HitokotoType type;

    /**
     * 句子出处（作品名）
     */
    private String from;

    /**
     * 作者、角色等
     */
    private String fromWho;

    /**
     * 创建时间（Unix 时间戳，字符串格式）
     */
    private String createTime;

    /**
     * 句子长度
     */
    private int length;
}

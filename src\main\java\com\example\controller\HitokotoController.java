package com.example.controller;

import com.example.bean.common.ApiResponse;
import com.example.bean.hitokoto.Hitokoto;
import com.example.service.HitokotoService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;

import java.io.IOException;

/**
 * 一言（Hitokoto）相关接口控制器
 * 提供获取随机一言和保存一言的接口
 * <AUTHOR>
 */
@Slf4j
@RestController
public class HitokotoController {
    /**
     * 注入 HitokotoService 服务，用于处理一言相关的业务逻辑
     */
    @Resource
    private HitokotoService hitokotoService;

    /**
     * 获取随机一言的接口
     * @return 返回包含随机一言的 ApiResponse 对象
     */
    @GetMapping("/api/hitokoto/random")
    public ApiResponse<Hitokoto> getRandomHitokoto() {
        try {
            // 调用服务层方法获取随机一言
            return ApiResponse.success(hitokotoService.getRandomHitokoto());
        } catch (IOException e) {
            // 捕获异常并记录日志，返回错误响应
            log.error("获取一言失败", e);
            return ApiResponse.error("服务器内部错误，获取一言失败");
        }
    }

    /**
     * 保存一言的接口
     * @param hitokoto 需要保存的一言对象，经过校验
     * @return 返回保存结果的 ApiResponse 对象
     */
    @PostMapping("/api/hitokoto")
    public ApiResponse<Hitokoto> postHitokoto(@Validated Hitokoto hitokoto) {
        try {
            // 调用服务层方法保存一言
            hitokotoService.saveHitokoto(hitokoto);
            return ApiResponse.success("保存一言成功");
        } catch (IllegalArgumentException e) {
            // 参数校验失败，返回 409 冲突错误
            log.warn("保存一言失败：{}", e.getMessage());
            return ApiResponse.error(409, e.getMessage());
        } catch (IOException e) {
            // 其他 IO 异常，记录日志并返回错误信息
            log.error("保存一言失败", e);
            return ApiResponse.error(e.getMessage());
        }
    }
}

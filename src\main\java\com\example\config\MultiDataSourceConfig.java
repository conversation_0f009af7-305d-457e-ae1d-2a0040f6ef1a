package com.example.config;

import com.zaxxer.hikari.HikariDataSource;
import org.springframework.ai.chat.memory.repository.jdbc.JdbcChatMemoryRepository;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.jdbc.DataSourceBuilder;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Primary;
import org.springframework.jdbc.core.JdbcTemplate;

import javax.sql.DataSource;

/**
 * 多数据源配置
 * 支持主业务数据库和ChatMemory数据库分离
 * 
 * <AUTHOR>
 */
@Configuration
public class MultiDataSourceConfig {

    /**
     * 主数据源 - 用于业务数据
     */
    @Primary
    @Bean(name = "primaryDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.primary")
    public DataSource primaryDataSource() {
        return DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * ChatMemory数据源 - 用于聊天记忆存储
     */
    @Bean(name = "chatMemoryDataSource")
    @ConfigurationProperties(prefix = "spring.datasource.chatmemory")
    public DataSource chatMemoryDataSource() {
        return DataSourceBuilder.create()
                .type(HikariDataSource.class)
                .build();
    }

    /**
     * 主业务的JdbcTemplate
     */
    @Primary
    @Bean(name = "primaryJdbcTemplate")
    public JdbcTemplate primaryJdbcTemplate(@Qualifier("primaryDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    /**
     * ChatMemory专用的JdbcTemplate
     */
    @Bean(name = "chatMemoryJdbcTemplate")
    public JdbcTemplate chatMemoryJdbcTemplate(@Qualifier("chatMemoryDataSource") DataSource dataSource) {
        return new JdbcTemplate(dataSource);
    }

    /**
     * 自定义JdbcChatMemoryRepository，使用专用数据源
     */
    @Bean
    public JdbcChatMemoryRepository jdbcChatMemoryRepository(@Qualifier("chatMemoryJdbcTemplate") JdbcTemplate jdbcTemplate) {
        return JdbcChatMemoryRepository.builder()
                .jdbcTemplate(jdbcTemplate)
                .build();
    }
}

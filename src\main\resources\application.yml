hitokoto:
  jsonPath: data/hitokoto.json
image:
  jsonPath: data/image.json
  imagePath: image

spring:
  ai:
    deepseek:
      base-url: https://api.deepseek.com/v1
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
          temperature: 0.8
    # ChatMemory JDBC 配置
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: always  # 让Spring AI自动创建表

  datasource:
    url: **************************************
    driver-class-name: com.mysql.cj.jdbc.Driver
    username: root
    password: 123456

server:
  port: 8088
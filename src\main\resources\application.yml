hitokoto:
  jsonPath: data/hitokoto.json
image:
  jsonPath: data/image.json
  imagePath: image

spring:
  ai:
    deepseek:
      base-url: https://api.deepseek.com/v1
      api-key: ***********************************
      chat:
        options:
          model: deepseek-chat
          temperature: 0.8
    # ChatMemory JDBC 配置
    chat:
      memory:
        repository:
          jdbc:
            initialize-schema: always  # 让Spring AI自动创建表
  # 数据库配置 - 使用H2内存数据库（如果MySQL不可用）
  datasource:
    url: jdbc:h2:mem:chatmemory;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE
    driver-class-name: org.h2.Driver
    username: sa
    password:

    # 如果MySQL可用，取消注释下面几行，注释上面的H2配置
    # url: ***********************************************************************************************************************
    # driver-class-name: com.mysql.cj.jdbc.Driver
    # username: root
    # password: 123456

server:
  port: 8088
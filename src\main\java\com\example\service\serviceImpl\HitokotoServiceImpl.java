package com.example.service.serviceImpl;

import com.example.bean.hitokoto.Hitokoto;
import com.example.bean.hitokoto.HitokotoType;
import com.example.service.HitokotoService;
import com.example.utils.DateFormatter;
import com.example.utils.JsonUtil;
import com.fasterxml.jackson.core.type.TypeReference;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
public class HitokotoServiceImpl implements HitokotoService {
    @Value("${hitokoto.jsonPath}")
    String jsonPath;

    @Override
    public Hitokoto getRandomHitokoto() throws IOException {
        return JsonUtil.getRandomElement(new File(jsonPath),
                new TypeReference< List<Hitokoto>>() {
                });
    }

    @Override
    public void saveHitokoto(Hitokoto hitokoto) throws IOException {
        processByPost(new File(jsonPath), hitokoto);
    }

    /**
     * 通过POST方式处理单条Hitokoto并保存到JSON文件。
     *
     * @param jsonFile  JSON文件
     * @param hitokoto  Hitokoto对象
     * @throws IOException              文件读写异常
     * @throws IllegalArgumentException 如果存在重复的Hitokoto
     */
    private void processByPost(File jsonFile, Hitokoto hitokoto) throws IOException {
        List<Hitokoto> list = JsonUtil.getList(jsonFile, new TypeReference<List<Hitokoto>>() {});
        if (isDuplicate(list, hitokoto.getHitokoto())) {
            throw new IllegalArgumentException("重复的 Hitokoto");
        }
        if (hitokoto.getType() == null) {
            hitokoto.setType(HitokotoType.OTHER);
        }
        fillHitokotoMeta(hitokoto);
        list.add(hitokoto);
        log.info("hitokoto: {}, 保存成功。", hitokoto);
        JsonUtil.saveToJson(jsonFile, list);
    }

    /**
     * 判断Hitokoto内容是否重复。
     *
     * @param list      已有Hitokoto列表
     * @param hitokoto  Hitokoto内容
     * @return 是否重复
     */
    private boolean isDuplicate(List<Hitokoto> list, String hitokoto) {
        return list.stream().anyMatch(t -> t.getHitokoto().equals(hitokoto));
    }

    /**
     * 填充Hitokoto的元数据（UUID、创建时间、长度）。
     *
     * @param hitokoto Hitokoto对象
     */
    private void fillHitokotoMeta(Hitokoto hitokoto) {
        hitokoto.setUuid(UUID.randomUUID().toString());
        hitokoto.setCreateTime(DateFormatter.getLocalDateTime());
        hitokoto.setLength(hitokoto.getHitokoto().length());
    }
}

package com.example.controller;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.PromptChatMemoryAdvisor;
import org.springframework.ai.chat.client.advisor.VectorStoreChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.memory.repository.jdbc.JdbcChatMemoryRepository;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.ai.vectorstore.VectorStore;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 记忆管理方式对比示例
 * 展示手动管理 vs 记忆顾问的区别
 * 
 * <AUTHOR>
 */
@RestController
public class MemoryComparisonController {

    @Resource
    private DeepSeekChatModel chatModel;
    
    @Resource
    private JdbcChatMemoryRepository chatMemoryRepository;

    // 可选：如果要测试VectorStoreChatMemoryAdvisor，需要注入VectorStore
    // @Resource
    // private VectorStore vectorStore;

    // 手动管理用的ChatMemory
    private ChatMemory manualChatMemory;

    // 三种记忆顾问的ChatClient
    private ChatClient messageChatClient;      // MessageChatMemoryAdvisor
    private ChatClient promptChatClient;       // PromptChatMemoryAdvisor
    private ChatClient vectorStoreChatClient;  // VectorStoreChatMemoryAdvisor

    @PostConstruct
    public void init() {
        // 初始化手动管理的ChatMemory
        this.manualChatMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(5)
                .build();

        // 为三种记忆顾问创建独立的ChatMemory实例
        ChatMemory messageChatMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(5)
                .build();

        ChatMemory promptChatMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(5)
                .build();

        // 1. MessageChatMemoryAdvisor - 将历史消息作为Message对象添加到Prompt
        this.messageChatClient = ChatClient.builder(chatModel)
                .defaultAdvisors(MessageChatMemoryAdvisor.builder(messageChatMemory).build())
                .build();

        // 2. PromptChatMemoryAdvisor - 将历史消息作为纯文本追加到系统提示
        this.promptChatClient = ChatClient.builder(chatModel)
                .defaultAdvisors(PromptChatMemoryAdvisor.builder(promptChatMemory).build())
                .build();

        // 3. VectorStoreChatMemoryAdvisor - 使用向量存储检索相关历史消息
        // 注意：需要配置VectorStore才能使用，这里先注释掉
        // this.vectorStoreChatClient = ChatClient.builder(chatModel)
        //         .defaultAdvisors(VectorStoreChatMemoryAdvisor.builder(vectorStore).build())
        //         .build();
    }

    /**
     * 方式1：手动管理记忆（你当前的方式）
     */
    @GetMapping("/memory/manual")
    public Map<String, Object> manualMemoryChat(
            @RequestParam(value = "message", defaultValue = "我叫张三") String message,
            @RequestParam(value = "conversationId", defaultValue = "manual-test") String conversationId) {
        
        try {
            // 1. 手动添加用户消息到记忆
            UserMessage userMessage = new UserMessage(message);
            manualChatMemory.add(conversationId, userMessage);
            
            // 2. 手动获取对话历史并创建prompt
            var prompt = new Prompt(manualChatMemory.get(conversationId));
            
            // 3. 调用AI模型
            ChatResponse response = chatModel.call(prompt);
            String aiReply = response.getResult().getOutput().getContent();
            
            // 4. 手动保存AI回复到记忆
            manualChatMemory.add(conversationId, response.getResult().getOutput());
            
            return Map.of(
                "method", "手动管理记忆",
                "conversationId", conversationId,
                "userMessage", message,
                "aiReply", aiReply,
                "historyCount", manualChatMemory.get(conversationId).size()
            );
            
        } catch (Exception e) {
            return Map.of(
                "error", e.getMessage(),
                "method", "手动管理记忆"
            );
        }
    }

    /**
     * 方式2：MessageChatMemoryAdvisor - 将历史消息作为Message对象添加
     */
    @GetMapping("/memory/message-advisor")
    public Map<String, Object> messageChatMemoryAdvisor(
            @RequestParam(value = "message", defaultValue = "我叫李四") String message,
            @RequestParam(value = "conversationId", defaultValue = "message-advisor-test") String conversationId) {

        try {
            String aiReply = messageChatClient.prompt()
                    .user(message)
                    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
                    .call()
                    .content();

            return Map.of(
                "method", "MessageChatMemoryAdvisor",
                "description", "将历史消息作为Message对象添加到Prompt中",
                "conversationId", conversationId,
                "userMessage", message,
                "aiReply", aiReply
            );

        } catch (Exception e) {
            return Map.of("error", e.getMessage(), "method", "MessageChatMemoryAdvisor");
        }
    }

    /**
     * 方式3：PromptChatMemoryAdvisor - 将历史消息作为纯文本追加到系统提示
     */
    @GetMapping("/memory/prompt-advisor")
    public Map<String, Object> promptChatMemoryAdvisor(
            @RequestParam(value = "message", defaultValue = "我叫王五") String message,
            @RequestParam(value = "conversationId", defaultValue = "prompt-advisor-test") String conversationId) {

        try {
            String aiReply = promptChatClient.prompt()
                    .user(message)
                    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
                    .call()
                    .content();

            return Map.of(
                "method", "PromptChatMemoryAdvisor",
                "description", "将历史消息作为纯文本追加到系统提示中",
                "conversationId", conversationId,
                "userMessage", message,
                "aiReply", aiReply
            );

        } catch (Exception e) {
            return Map.of("error", e.getMessage(), "method", "PromptChatMemoryAdvisor");
        }
    }

    /**
     * 方式4：VectorStoreChatMemoryAdvisor - 使用向量存储检索相关历史消息
     * 注意：需要配置VectorStore才能使用
     */
    @GetMapping("/memory/vector-advisor")
    public Map<String, Object> vectorStoreChatMemoryAdvisor(
            @RequestParam(value = "message", defaultValue = "我叫赵六") String message,
            @RequestParam(value = "conversationId", defaultValue = "vector-advisor-test") String conversationId) {

        return Map.of(
            "method", "VectorStoreChatMemoryAdvisor",
            "description", "使用向量存储检索相关历史消息并追加到系统消息",
            "status", "需要配置VectorStore",
            "note", "这种方式适合大量历史数据的智能检索场景",
            "example", "在RAG应用中，可以从向量数据库中检索最相关的历史对话"
        );
    }

    /**
     * 完整测试指南 - 四种记忆管理方式对比
     */
    @GetMapping("/memory/test-guide")
    public Map<String, Object> testGuide() {
        return Map.of(
            "title", "四种记忆管理方式对比测试指南",
            "methods", Map.of(
                "1_手动管理", Map.of(
                    "description", "传统方式，手动管理所有记忆操作",
                    "test1", "/memory/manual?message=我叫张三&conversationId=manual-test",
                    "test2", "/memory/manual?message=我叫什么名字？&conversationId=manual-test"
                ),
                "2_MessageChatMemoryAdvisor", Map.of(
                    "description", "将历史消息作为Message对象添加到Prompt中",
                    "test1", "/memory/message-advisor?message=我叫李四&conversationId=message-test",
                    "test2", "/memory/message-advisor?message=我叫什么名字？&conversationId=message-test"
                ),
                "3_PromptChatMemoryAdvisor", Map.of(
                    "description", "将历史消息作为纯文本追加到系统提示中",
                    "test1", "/memory/prompt-advisor?message=我叫王五&conversationId=prompt-test",
                    "test2", "/memory/prompt-advisor?message=我叫什么名字？&conversationId=prompt-test"
                ),
                "4_VectorStoreChatMemoryAdvisor", Map.of(
                    "description", "使用向量存储检索相关历史消息（需要配置VectorStore）",
                    "test", "/memory/vector-advisor",
                    "note", "这个示例只展示概念，实际使用需要配置向量数据库"
                )
            ),
            "differences", Map.of(
                "手动管理", "完全控制，代码较多，需要处理所有细节",
                "MessageChatMemoryAdvisor", "保持消息结构完整，适合需要元数据的场景",
                "PromptChatMemoryAdvisor", "简单文本格式，适合纯文本对话",
                "VectorStoreChatMemoryAdvisor", "智能检索，适合大量历史数据的场景"
            ),
            "storage", "所有方式都使用相同的JdbcChatMemoryRepository存储到数据库"
        );
    }
}

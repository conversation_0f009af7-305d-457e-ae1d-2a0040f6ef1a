package com.example.controller;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import org.springframework.ai.chat.client.ChatClient;
import org.springframework.ai.chat.client.advisor.MessageChatMemoryAdvisor;
import org.springframework.ai.chat.memory.ChatMemory;
import org.springframework.ai.chat.memory.MessageWindowChatMemory;
import org.springframework.ai.chat.memory.repository.jdbc.JdbcChatMemoryRepository;
import org.springframework.ai.chat.messages.UserMessage;
import org.springframework.ai.chat.model.ChatResponse;
import org.springframework.ai.chat.prompt.Prompt;
import org.springframework.ai.deepseek.DeepSeekChatModel;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

/**
 * 记忆管理方式对比示例
 * 展示手动管理 vs 记忆顾问的区别
 * 
 * <AUTHOR>
 */
@RestController
public class MemoryComparisonController {

    @Resource
    private DeepSeekChatModel chatModel;
    
    @Resource
    private JdbcChatMemoryRepository chatMemoryRepository;
    
    // 手动管理用的ChatMemory
    private ChatMemory manualChatMemory;
    
    // 记忆顾问用的ChatClient
    private ChatClient advisorChatClient;

    @PostConstruct
    public void init() {
        // 初始化手动管理的ChatMemory
        this.manualChatMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(5)
                .build();
        
        // 初始化带记忆顾问的ChatClient
        ChatMemory advisorChatMemory = MessageWindowChatMemory.builder()
                .chatMemoryRepository(chatMemoryRepository)
                .maxMessages(5)
                .build();
                
        this.advisorChatClient = ChatClient.builder(chatModel)
                .defaultAdvisors(MessageChatMemoryAdvisor.builder(advisorChatMemory).build())
                .build();
    }

    /**
     * 方式1：手动管理记忆（你当前的方式）
     */
    @GetMapping("/memory/manual")
    public Map<String, Object> manualMemoryChat(
            @RequestParam(value = "message", defaultValue = "我叫张三") String message,
            @RequestParam(value = "conversationId", defaultValue = "manual-test") String conversationId) {
        
        try {
            // 1. 手动添加用户消息到记忆
            UserMessage userMessage = new UserMessage(message);
            manualChatMemory.add(conversationId, userMessage);
            
            // 2. 手动获取对话历史并创建prompt
            var prompt = new Prompt(manualChatMemory.get(conversationId));
            
            // 3. 调用AI模型
            ChatResponse response = chatModel.call(prompt);
            String aiReply = response.getResult().getOutput().getContent();
            
            // 4. 手动保存AI回复到记忆
            manualChatMemory.add(conversationId, response.getResult().getOutput());
            
            return Map.of(
                "method", "手动管理记忆",
                "conversationId", conversationId,
                "userMessage", message,
                "aiReply", aiReply,
                "historyCount", manualChatMemory.get(conversationId).size()
            );
            
        } catch (Exception e) {
            return Map.of(
                "error", e.getMessage(),
                "method", "手动管理记忆"
            );
        }
    }

    /**
     * 方式2：使用记忆顾问（推荐方式）
     */
    @GetMapping("/memory/advisor")
    public Map<String, Object> advisorMemoryChat(
            @RequestParam(value = "message", defaultValue = "我叫李四") String message,
            @RequestParam(value = "conversationId", defaultValue = "advisor-test") String conversationId) {
        
        try {
            // 一行代码搞定！记忆顾问自动处理所有记忆操作
            String aiReply = advisorChatClient.prompt()
                    .user(message)
                    .advisors(a -> a.param(ChatMemory.CONVERSATION_ID, conversationId))
                    .call()
                    .content();
            
            return Map.of(
                "method", "记忆顾问",
                "conversationId", conversationId,
                "userMessage", message,
                "aiReply", aiReply,
                "note", "记忆顾问自动管理了所有记忆操作"
            );
            
        } catch (Exception e) {
            return Map.of(
                "error", e.getMessage(),
                "method", "记忆顾问"
            );
        }
    }

    /**
     * 测试指南
     */
    @GetMapping("/memory/test-guide")
    public Map<String, Object> testGuide() {
        return Map.of(
            "title", "记忆管理对比测试指南",
            "steps", Map.of(
                "step1", "先访问: /memory/manual?message=我叫张三&conversationId=test1",
                "step2", "再访问: /memory/manual?message=我叫什么名字？&conversationId=test1",
                "step3", "然后访问: /memory/advisor?message=我叫李四&conversationId=test2", 
                "step4", "最后访问: /memory/advisor?message=我叫什么名字？&conversationId=test2"
            ),
            "comparison", Map.of(
                "手动管理", "需要4步：添加用户消息 → 获取历史 → 调用AI → 保存AI回复",
                "记忆顾问", "只需1步：调用ChatClient，其他全自动"
            ),
            "database", "两种方式都使用相同的JdbcChatMemoryRepository存储到数据库"
        );
    }
}

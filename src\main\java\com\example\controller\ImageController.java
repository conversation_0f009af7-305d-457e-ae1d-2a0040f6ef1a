package com.example.controller;

import com.example.bean.common.ApiResponse;
import com.example.bean.image.Image;
import com.example.bean.image.ImageType;
import com.example.bean.image.ImageWrapper;
import com.example.service.ImageService;
import jakarta.annotation.Resource;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;

/**
 * <AUTHOR>
 */
@RestController
public class ImageController {
    @Resource
    private ImageService imageService;

    @GetMapping("/api/image/random")
    public ResponseEntity<byte[]> getRandomImage() throws IOException {
        ImageWrapper imageWrapper = imageService.getImage();

        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.valueOf(imageWrapper.getMediaType()));
        headers.setContentLength(imageWrapper.getData().length);
        return new ResponseEntity<>(imageWrapper.getData(), headers, HttpStatus.OK);
    }

    @PostMapping("/api/image/upload")
    public ApiResponse<Image> saveImage(@RequestParam("file") MultipartFile file, @RequestParam("type") String typeStr) throws IOException {
        ImageType type = ImageType.from(typeStr);
        imageService.saveImage(file, type);
        return ApiResponse.success("保存成功");
    }
}

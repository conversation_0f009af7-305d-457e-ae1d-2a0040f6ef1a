package com.example.bean.hitokoto;

import com.fasterxml.jackson.annotation.JsonCreator;
import com.fasterxml.jackson.annotation.JsonValue;

/**
 * 表示一言（Hitokoto）的类型枚举。
 * 用于区分不同类别的短句。
 * 支持 Jackson 的序列化与反序列化。
 *
 * <AUTHOR>
 */
public enum HitokotoType {
    /** 动画 */
    ANIME,
    /** 游戏 */
    GAME,
    /** 电影 */
    MOVIE,
    /** 文学 */
    LITERATURE,
    /** 名言 */
    QUOTE,
    /** 爱情 */
    LOVE,
    /** 网络 */
    INTERNET,
    /** 音乐 */
    MUSIC,
    /** 其他 */
    OTHER;

    /**
     * 通过字符串值获取对应的枚举类型，忽略大小写。
     * 若无匹配项，返回 OTHER。
     *
     * @param value 字符串类型
     * @return 对应的 HitokotoType 枚举
     */
    @JsonCreator
    public static HitokotoType from(String value) {
        if (value == null) {
            return OTHER;
        }
        try {
            return HitokotoType.valueOf(value.trim().toUpperCase());
        } catch (IllegalArgumentException e) {
            return OTHER;
        }
    }

    /**
     * 获取枚举的字符串值（小写），用于序列化。
     *
     * @return 小写字符串
     */
    @JsonValue
    public String toValue() {
        return this.name().toLowerCase();
    }
}

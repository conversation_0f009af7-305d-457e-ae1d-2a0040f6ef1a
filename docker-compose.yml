services:
  # MySQL数据库
  mysql:
    image: mysql:8.0
    container_name: chatmemory-mysql
    environment:
      MYSQL_ROOT_PASSWORD: 123456
      MYSQL_DATABASE: chatmemory
      MYSQL_USER: chatuser
      MYSQL_PASSWORD: 123456
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
    command: --default-authentication-plugin=mysql_native_password

  drealapi:
    build: .
    container_name: drealapi
    ports:
      - "3000:8080"
    volumes:
      - ./data/hitokoto.json:/app/data/hitokoto.json
      - ./log:/app/log
    depends_on:
      - mysql

volumes:
  mysql_data: